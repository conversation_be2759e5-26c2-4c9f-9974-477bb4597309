SELECT 
    ISNULL(CAST(SERIALKEY AS NVARCHAR(10)), '') AS SERIALKEY,
    ISNULL(WHSEID, '') AS WHSEID,
    ISNULL(USERKEY, '') AS USERKEY,
    ISNULL(PRIORITYTASKTYPE, '') AS PRIORITYTASKTYPE,
    ISNULL(STRATEGYKEY, '') AS STRATEGYKEY,
    ISNULL(EQUIPM<PERSON><PERSON>ROFILEKEY, '') AS EQUIPMENTPROFILEKEY,
    ISNULL(LASTCASEIDPICKED, '') AS LASTCASEIDPICKED,
    ISNULL(LASTWAVEKEY, '') AS LASTWAVEKEY,
    ISNULL(TTMSTRATEGYKEY, '') AS TTMSTRATEGYKEY,
    ISNULL(LASTLOC, '') AS LASTLOC,
    ISNULL(LASTTOLOC, '') AS LASTTOLOC,
    ISNULL(USR_NAME, '') AS USR_NAME,
    ISNULL(USR_STATUS, 0) AS USR_STATUS,
    ISNULL(USR_FNAME, '') AS USR_FNAME,
    ISNULL(USR_LNAME, '') AS USR_LNAME,
    -- ... 其他字段类似处理
FROM TASKMANAGERUSER