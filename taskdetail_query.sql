SELECT 
    ISNULL(CAST(SERIALKEY AS NVARCHAR(10)), '') AS SERIALKEY,
    ISNULL(WHSEID, '') AS WHSEID,
    ISNULL(TASKDETAILKEY, '') AS TASKDETAILKEY,
    ISNULL(TASKTYPE, '') AS TASKTYPE,
    ISNULL(STORERKEY, '') AS STORERKEY,
    ISNULL(SKU, '') AS SKU,
    ISNULL(LOT, '') AS LOT,
    ISNULL(UOM, '') AS UOM,
    ISNULL(UOMQTY, 0) AS UOMQTY,
    ISNULL(QTY, 0) AS QTY,
    ISNULL(FROMLOC, '') AS FROMLOC,
    ISNULL(LOGICALFROMLOC, '') AS LOGICALFROMLOC,
    ISNULL(FROMID, '') AS FROMID,
    ISNULL(TOLOC, '') AS TOLOC,
    ISNULL(LOGICALTOLOC, '') AS LOGICALTOLOC,
    ISNULL(TOID, '') AS TOID,
    ISNULL(CASEID, '') AS CASEID,
    ISNULL(PICKMETHOD, '') AS PICKMETHOD,
    ISNULL(STATUS, '') AS STATUS,
    ISNULL(STATUSMSG, '') AS STATUSMSG,
    ISNULL(PRIORITY, '') AS PRIORITY,
    ISNULL(SOURCEPRIORITY, '') AS SOURCEPRIORITY,
    ISNULL(HOLDKEY, '') AS HOLDKEY,
    ISNULL(USERKEY, '') AS USERKEY,
    ISNULL(USERPOSITION, '') AS USERPOSITION,
    ISNULL(USERKEYOVERRIDE, '') AS USERKEYOVERRIDE,
    ISNULL(STARTTIME, '1900-01-01') AS STARTTIME,
    ISNULL(ENDTIME, '1900-01-01') AS ENDTIME,
    -- ... 其他字段类似处理
FROM TASKDETAIL