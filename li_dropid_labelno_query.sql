SELECT 
    ISNULL(CAST(SERIALKEY AS NVARCHAR(10)), '') AS SERIALKEY,
    ISNULL(WHSEID, '') AS WHSEID,
    ISNULL(LI_LABEL_NO, '') AS LI_LABEL_NO,
    ISNULL(LI_DROPID, '') AS LI_DROPID,
    ISNULL(LI_CHILDID, '') AS LI_CHILDID,
    ISNULL(LI_NUMBER_OF_PACKAGE, 0) AS LI_NUMBER_OF_PACKAGE,
    ISNULL(LI_CONSOLIDATED_CHECK_KBN, '') AS LI_CONSOLIDATED_CHECK_KBN,
    ISNULL(LI_ERROR_CODE, '') AS LI_ERROR_CODE,
    ISNULL(LI_EXTERNORDERKEY, '') AS LI_EXTERNORDERKEY,
    ISNULL(LI_ORDERDETAIL_SUSR2, '') AS LI_ORDERDETAIL_SUSR2,
    ISNULL(ADDDATE, '1900-01-01') AS ADDDATE,
    ISNULL(ADDWHO, '') AS ADDWHO,
    ISNULL(EDITDATE, '1900-01-01') AS EDITDATE,
    ISNULL(EDITWHO, '') AS EDITWHO
FROM LI_DROPID_LABELNO